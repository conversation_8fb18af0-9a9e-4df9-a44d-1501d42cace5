// Generated protobuf code for person.proto
// This is a simplified version - in production, use protoc to generate this file

const jspb = require('google-protobuf');
const goog = jspb;

// Person message
const Person = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(Person, jspb.Message);

Person.prototype.getId = function() {
  return jspb.Message.getFieldWithDefault(this, 1, 0);
};

Person.prototype.setId = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};

Person.prototype.getFirstName = function() {
  return jspb.Message.getFieldWithDefault(this, 2, "");
};

Person.prototype.setFirstName = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};

Person.prototype.getLastName = function() {
  return jspb.Message.getFieldWithDefault(this, 3, "");
};

Person.prototype.setLastName = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};

Person.prototype.getEmail = function() {
  return jspb.Message.getFieldWithDefault(this, 4, "");
};

Person.prototype.setEmail = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};

Person.prototype.getPhone = function() {
  return jspb.Message.getFieldWithDefault(this, 5, "");
};

Person.prototype.setPhone = function(value) {
  return jspb.Message.setProto3StringField(this, 5, value);
};

Person.prototype.getAddress = function() {
  return jspb.Message.getFieldWithDefault(this, 6, "");
};

Person.prototype.setAddress = function(value) {
  return jspb.Message.setProto3StringField(this, 6, value);
};

Person.prototype.getCity = function() {
  return jspb.Message.getFieldWithDefault(this, 7, "");
};

Person.prototype.setCity = function(value) {
  return jspb.Message.setProto3StringField(this, 7, value);
};

Person.prototype.getState = function() {
  return jspb.Message.getFieldWithDefault(this, 8, "");
};

Person.prototype.setState = function(value) {
  return jspb.Message.setProto3StringField(this, 8, value);
};

Person.prototype.getZipCode = function() {
  return jspb.Message.getFieldWithDefault(this, 9, "");
};

Person.prototype.setZipCode = function(value) {
  return jspb.Message.setProto3StringField(this, 9, value);
};

Person.prototype.getCountry = function() {
  return jspb.Message.getFieldWithDefault(this, 10, "");
};

Person.prototype.setCountry = function(value) {
  return jspb.Message.setProto3StringField(this, 10, value);
};

Person.prototype.getCreatedAt = function() {
  return jspb.Message.getFieldWithDefault(this, 11, 0);
};

Person.prototype.setCreatedAt = function(value) {
  return jspb.Message.setProto3IntField(this, 11, value);
};

Person.prototype.getUpdatedAt = function() {
  return jspb.Message.getFieldWithDefault(this, 12, 0);
};

Person.prototype.setUpdatedAt = function(value) {
  return jspb.Message.setProto3IntField(this, 12, value);
};

Person.prototype.serializeBinary = function() {
  const writer = new jspb.BinaryWriter();
  Person.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};

Person.serializeBinaryToWriter = function(message, writer) {
  const f = undefined;
  f = message.getId();
  if (f !== 0) {
    writer.writeInt32(1, f);
  }
  f = message.getFirstName();
  if (f.length > 0) {
    writer.writeString(2, f);
  }
  f = message.getLastName();
  if (f.length > 0) {
    writer.writeString(3, f);
  }
  f = message.getEmail();
  if (f.length > 0) {
    writer.writeString(4, f);
  }
  f = message.getPhone();
  if (f.length > 0) {
    writer.writeString(5, f);
  }
  f = message.getAddress();
  if (f.length > 0) {
    writer.writeString(6, f);
  }
  f = message.getCity();
  if (f.length > 0) {
    writer.writeString(7, f);
  }
  f = message.getState();
  if (f.length > 0) {
    writer.writeString(8, f);
  }
  f = message.getZipCode();
  if (f.length > 0) {
    writer.writeString(9, f);
  }
  f = message.getCountry();
  if (f.length > 0) {
    writer.writeString(10, f);
  }
  f = message.getCreatedAt();
  if (f !== 0) {
    writer.writeInt64(11, f);
  }
  f = message.getUpdatedAt();
  if (f !== 0) {
    writer.writeInt64(12, f);
  }
};

Person.deserializeBinary = function(bytes) {
  const reader = new jspb.BinaryReader(bytes);
  const msg = new Person();
  return Person.deserializeBinaryFromReader(msg, reader);
};

Person.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    const field = reader.getFieldNumber();
    switch (field) {
    case 1:
      const value1 = reader.readInt32();
      msg.setId(value1);
      break;
    case 2:
      const value2 = reader.readString();
      msg.setFirstName(value2);
      break;
    case 3:
      const value3 = reader.readString();
      msg.setLastName(value3);
      break;
    case 4:
      const value4 = reader.readString();
      msg.setEmail(value4);
      break;
    case 5:
      const value5 = reader.readString();
      msg.setPhone(value5);
      break;
    case 6:
      const value6 = reader.readString();
      msg.setAddress(value6);
      break;
    case 7:
      const value7 = reader.readString();
      msg.setCity(value7);
      break;
    case 8:
      const value8 = reader.readString();
      msg.setState(value8);
      break;
    case 9:
      const value9 = reader.readString();
      msg.setZipCode(value9);
      break;
    case 10:
      const value10 = reader.readString();
      msg.setCountry(value10);
      break;
    case 11:
      const value11 = reader.readInt64();
      msg.setCreatedAt(value11);
      break;
    case 12:
      const value12 = reader.readInt64();
      msg.setUpdatedAt(value12);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};

module.exports = {
  Person: Person
};
