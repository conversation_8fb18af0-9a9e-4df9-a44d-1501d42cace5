#!/usr/bin/env node

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Starting Protocol Buffers vs JSON Performance Demo');
console.log('================================================');

// Check if database exists
const dbPath = path.join(__dirname, 'server', 'database.sqlite');
if (!fs.existsSync(dbPath)) {
    console.log('📊 Setting up database...');
    
    const setupDb = spawn('node', ['server/setup-db.js'], {
        stdio: 'inherit',
        cwd: __dirname
    });
    
    setupDb.on('close', (code) => {
        if (code === 0) {
            console.log('✅ Database setup completed');
            startServer();
        } else {
            console.error('❌ Database setup failed');
            process.exit(1);
        }
    });
} else {
    console.log('✅ Database already exists');
    startServer();
}

function startServer() {
    console.log('🌐 Starting server...');
    
    const server = spawn('node', ['server/index.js'], {
        stdio: 'inherit',
        cwd: __dirname
    });
    
    server.on('close', (code) => {
        console.log(`Server exited with code ${code}`);
    });
    
    // Handle graceful shutdown
    process.on('SIGINT', () => {
        console.log('\n🛑 Shutting down server...');
        server.kill('SIGINT');
        process.exit(0);
    });
    
    // Display instructions after a short delay
    setTimeout(() => {
        console.log('\n📖 Demo Instructions:');
        console.log('1. Open your browser to http://localhost:3000');
        console.log('2. Click "Load Persons (JSON)" to test JSON API');
        console.log('3. Click "Load Persons (Protobuf)" to test Protobuf API');
        console.log('4. Click "Enhanced Performance Test" to see detailed comparison');
        console.log('5. Try updating persons using both APIs');
        console.log('\n💡 The Enhanced Performance Test shows realistic protobuf benefits:');
        console.log('   - Data size reduction (40-60% smaller)');
        console.log('   - Faster network transfer times');
        console.log('   - Overall performance improvements');
        console.log('\nPress Ctrl+C to stop the server');
    }, 2000);
}
