const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const { Person } = require('../generated/person_pb');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.raw({ type: 'application/x-protobuf' }));

// Serve static files from client directory
app.use(express.static(path.join(__dirname, '../client')));

// Database connection
const dbPath = path.join(__dirname, 'database.sqlite');
const db = new sqlite3.Database(dbPath);

// Helper function to convert database row to Person object
function dbRowToPerson(row) {
  return {
    id: row.id,
    first_name: row.first_name,
    last_name: row.last_name,
    email: row.email,
    phone: row.phone || '',
    address: row.address || '',
    city: row.city || '',
    state: row.state || '',
    zip_code: row.zip_code || '',
    country: row.country || '',
    created_at: row.created_at,
    updated_at: row.updated_at
  };
}

// Helper function to convert Person object to protobuf Person
function personToProtobuf(personData) {
  const person = new Person();
  person.setId(personData.id || 0);
  person.setFirstName(personData.first_name || '');
  person.setLastName(personData.last_name || '');
  person.setEmail(personData.email || '');
  person.setPhone(personData.phone || '');
  person.setAddress(personData.address || '');
  person.setCity(personData.city || '');
  person.setState(personData.state || '');
  person.setZipCode(personData.zip_code || '');
  person.setCountry(personData.country || '');
  person.setCreatedAt(personData.created_at || 0);
  person.setUpdatedAt(personData.updated_at || 0);
  return person;
}

// JSON API Routes

// Get all persons (JSON)
app.get('/api/json/persons', (req, res) => {
  const startTime = Date.now();
  const limit = parseInt(req.query.limit) || 100;
  const offset = parseInt(req.query.offset) || 0;

  db.all('SELECT * FROM persons LIMIT ? OFFSET ?', [limit, offset], (err, rows) => {
    if (err) {
      return res.status(500).json({ success: false, message: err.message });
    }

    const persons = rows.map(dbRowToPerson);
    const processingTime = Date.now() - startTime;

    res.json({
      success: true,
      message: 'Persons retrieved successfully',
      persons: persons,
      total_count: persons.length,
      processing_time_ms: processingTime
    });
  });
});

// Get person by ID (JSON)
app.get('/api/json/persons/:id', (req, res) => {
  const startTime = Date.now();
  const id = parseInt(req.params.id);

  db.get('SELECT * FROM persons WHERE id = ?', [id], (err, row) => {
    if (err) {
      return res.status(500).json({ success: false, message: err.message });
    }

    if (!row) {
      return res.status(404).json({ success: false, message: 'Person not found' });
    }

    const person = dbRowToPerson(row);
    const processingTime = Date.now() - startTime;

    res.json({
      success: true,
      message: 'Person retrieved successfully',
      person: person,
      processing_time_ms: processingTime
    });
  });
});

// Update person (JSON)
app.put('/api/json/persons/:id', (req, res) => {
  const startTime = Date.now();
  const id = parseInt(req.params.id);
  const person = req.body;

  const updateSQL = `
    UPDATE persons 
    SET first_name = ?, last_name = ?, email = ?, phone = ?, 
        address = ?, city = ?, state = ?, zip_code = ?, country = ?, updated_at = ?
    WHERE id = ?
  `;

  const now = Date.now();
  const params = [
    person.first_name, person.last_name, person.email, person.phone,
    person.address, person.city, person.state, person.zip_code, person.country,
    now, id
  ];

  db.run(updateSQL, params, function(err) {
    if (err) {
      return res.status(500).json({ success: false, message: err.message });
    }

    if (this.changes === 0) {
      return res.status(404).json({ success: false, message: 'Person not found' });
    }

    // Get the updated person
    db.get('SELECT * FROM persons WHERE id = ?', [id], (err, row) => {
      if (err) {
        return res.status(500).json({ success: false, message: err.message });
      }

      const updatedPerson = dbRowToPerson(row);
      const processingTime = Date.now() - startTime;

      res.json({
        success: true,
        message: 'Person updated successfully',
        person: updatedPerson,
        processing_time_ms: processingTime
      });
    });
  });
});

// Create person (JSON)
app.post('/api/json/persons', (req, res) => {
  const startTime = Date.now();
  const person = req.body;

  const insertSQL = `
    INSERT INTO persons (first_name, last_name, email, phone, address, city, state, zip_code, country, created_at, updated_at)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `;

  const now = Date.now();
  const params = [
    person.first_name, person.last_name, person.email, person.phone,
    person.address, person.city, person.state, person.zip_code, person.country,
    now, now
  ];

  db.run(insertSQL, params, function(err) {
    if (err) {
      return res.status(500).json({ success: false, message: err.message });
    }

    // Get the created person
    db.get('SELECT * FROM persons WHERE id = ?', [this.lastID], (err, row) => {
      if (err) {
        return res.status(500).json({ success: false, message: err.message });
      }

      const createdPerson = dbRowToPerson(row);
      const processingTime = Date.now() - startTime;

      res.status(201).json({
        success: true,
        message: 'Person created successfully',
        person: createdPerson,
        processing_time_ms: processingTime
      });
    });
  });
});

// Delete person (JSON)
app.delete('/api/json/persons/:id', (req, res) => {
  const startTime = Date.now();
  const id = parseInt(req.params.id);

  db.run('DELETE FROM persons WHERE id = ?', [id], function(err) {
    if (err) {
      return res.status(500).json({ success: false, message: err.message });
    }

    if (this.changes === 0) {
      return res.status(404).json({ success: false, message: 'Person not found' });
    }

    const processingTime = Date.now() - startTime;

    res.json({
      success: true,
      message: 'Person deleted successfully',
      processing_time_ms: processingTime
    });
  });
});

// Protobuf API Routes

// Get all persons (Protobuf)
app.get('/api/protobuf/persons', (req, res) => {
  const startTime = Date.now();
  const limit = parseInt(req.query.limit) || 100;
  const offset = parseInt(req.query.offset) || 0;

  db.all('SELECT * FROM persons LIMIT ? OFFSET ?', [limit, offset], (err, rows) => {
    if (err) {
      res.status(500).set('Content-Type', 'application/x-protobuf');
      return res.send(Buffer.from(JSON.stringify({ success: false, message: err.message })));
    }

    const persons = rows.map(row => {
      const personData = dbRowToPerson(row);
      return personToProtobuf(personData);
    });

    const processingTime = Date.now() - startTime;

    // For simplicity, we'll send a JSON response with protobuf data
    // In a real implementation, you'd create proper protobuf response messages
    const response = {
      success: true,
      message: 'Persons retrieved successfully',
      persons: persons.map(p => ({
        id: p.getId(),
        first_name: p.getFirstName(),
        last_name: p.getLastName(),
        email: p.getEmail(),
        phone: p.getPhone(),
        address: p.getAddress(),
        city: p.getCity(),
        state: p.getState(),
        zip_code: p.getZipCode(),
        country: p.getCountry(),
        created_at: p.getCreatedAt(),
        updated_at: p.getUpdatedAt()
      })),
      total_count: persons.length,
      processing_time_ms: processingTime
    };

    res.set('Content-Type', 'application/x-protobuf');
    res.send(Buffer.from(JSON.stringify(response)));
  });
});

// Update person (Protobuf)
app.put('/api/protobuf/persons/:id', (req, res) => {
  const startTime = Date.now();
  const id = parseInt(req.params.id);

  try {
    // Parse protobuf data from request body
    let personData;
    if (req.body && req.body.length > 0) {
      // In a real implementation, you'd deserialize the protobuf data
      // For now, we'll assume JSON data for simplicity
      personData = JSON.parse(req.body.toString());
    } else {
      return res.status(400).send(Buffer.from(JSON.stringify({ success: false, message: 'Invalid request body' })));
    }

    const updateSQL = `
      UPDATE persons
      SET first_name = ?, last_name = ?, email = ?, phone = ?,
          address = ?, city = ?, state = ?, zip_code = ?, country = ?, updated_at = ?
      WHERE id = ?
    `;

    const now = Date.now();
    const params = [
      personData.first_name, personData.last_name, personData.email, personData.phone,
      personData.address, personData.city, personData.state, personData.zip_code, personData.country,
      now, id
    ];

    db.run(updateSQL, params, function(err) {
      if (err) {
        res.status(500).set('Content-Type', 'application/x-protobuf');
        return res.send(Buffer.from(JSON.stringify({ success: false, message: err.message })));
      }

      if (this.changes === 0) {
        res.status(404).set('Content-Type', 'application/x-protobuf');
        return res.send(Buffer.from(JSON.stringify({ success: false, message: 'Person not found' })));
      }

      // Get the updated person
      db.get('SELECT * FROM persons WHERE id = ?', [id], (err, row) => {
        if (err) {
          res.status(500).set('Content-Type', 'application/x-protobuf');
          return res.send(Buffer.from(JSON.stringify({ success: false, message: err.message })));
        }

        const updatedPersonData = dbRowToPerson(row);
        const updatedPerson = personToProtobuf(updatedPersonData);
        const processingTime = Date.now() - startTime;

        const response = {
          success: true,
          message: 'Person updated successfully',
          person: {
            id: updatedPerson.getId(),
            first_name: updatedPerson.getFirstName(),
            last_name: updatedPerson.getLastName(),
            email: updatedPerson.getEmail(),
            phone: updatedPerson.getPhone(),
            address: updatedPerson.getAddress(),
            city: updatedPerson.getCity(),
            state: updatedPerson.getState(),
            zip_code: updatedPerson.getZipCode(),
            country: updatedPerson.getCountry(),
            created_at: updatedPerson.getCreatedAt(),
            updated_at: updatedPerson.getUpdatedAt()
          },
          processing_time_ms: processingTime
        };

        res.set('Content-Type', 'application/x-protobuf');
        res.send(Buffer.from(JSON.stringify(response)));
      });
    });
  } catch (error) {
    res.status(400).set('Content-Type', 'application/x-protobuf');
    res.send(Buffer.from(JSON.stringify({ success: false, message: 'Invalid protobuf data' })));
  }
});

// Start server
app.listen(PORT, () => {
  console.log(`Server running on http://localhost:${PORT}`);
  console.log('API endpoints:');
  console.log('  JSON API: /api/json/persons');
  console.log('  Protobuf API: /api/protobuf/persons');
});
