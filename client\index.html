<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Protobuf vs JSON Performance Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        h2 {
            color: #555;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background-color: #0056b3;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-success:hover {
            background-color: #1e7e34;
        }
        .btn-warning {
            background-color: #ffc107;
            color: black;
        }
        .btn-warning:hover {
            background-color: #e0a800;
        }
        .performance-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        .stat-card.protobuf {
            border-left-color: #28a745;
        }
        .stat-card.json {
            border-left-color: #dc3545;
        }
        .stat-title {
            font-weight: bold;
            margin-bottom: 10px;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }
        .comparison {
            background: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            margin-top: 20px;
        }
        .person-form {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }
        .form-group {
            display: flex;
            flex-direction: column;
        }
        label {
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .results {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
        }
        .log-entry {
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        .log-success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
        }
        .log-error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
        }
        .log-info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
        }
        .persons-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .person-card {
            background: white;
            padding: 15px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        .person-name {
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 10px;
        }
        .person-details {
            font-size: 14px;
            color: #666;
        }
    </style>
</head>
<body>
    <h1>Protocol Buffers vs JSON Performance Demo</h1>
    
    <div class="container">
        <h2>Performance Statistics</h2>
        <div class="performance-stats">
            <div class="stat-card json">
                <div class="stat-title">JSON API Response Time</div>
                <div class="stat-value" id="jsonTime">-</div>
                <div>milliseconds</div>
            </div>
            <div class="stat-card protobuf">
                <div class="stat-title">Protobuf API Response Time</div>
                <div class="stat-value" id="protobufTime">-</div>
                <div>milliseconds</div>
            </div>
        </div>
        <div class="comparison" id="comparison" style="display: none;">
            <strong>Performance Comparison:</strong> <span id="comparisonText"></span>
        </div>
    </div>

    <div class="container">
        <h2>API Testing Controls</h2>
        <div class="controls">
            <button class="btn-primary" onclick="loadPersonsJSON()">Load Persons (JSON)</button>
            <button class="btn-success" onclick="loadPersonsProtobuf()">Load Persons (Protobuf)</button>
            <button class="btn-warning" onclick="runPerformanceTest()">Basic Performance Test</button>
            <button class="btn-warning" onclick="runEnhancedPerformanceTest()">Enhanced Performance Test</button>
            <button class="btn-primary" onclick="clearResults()">Clear Results</button>
        </div>
    </div>

    <div class="container">
        <h2>Update Person</h2>
        <div class="person-form">
            <div class="form-group">
                <label for="personId">Person ID:</label>
                <input type="number" id="personId" value="1">
            </div>
            <div class="form-group">
                <label for="firstName">First Name:</label>
                <input type="text" id="firstName" value="John">
            </div>
            <div class="form-group">
                <label for="lastName">Last Name:</label>
                <input type="text" id="lastName" value="Doe">
            </div>
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" value="<EMAIL>">
            </div>
            <div class="form-group">
                <label for="phone">Phone:</label>
                <input type="text" id="phone" value="******-0123">
            </div>
            <div class="form-group">
                <label for="address">Address:</label>
                <input type="text" id="address" value="123 Main St">
            </div>
            <div class="form-group">
                <label for="city">City:</label>
                <input type="text" id="city" value="Anytown">
            </div>
            <div class="form-group">
                <label for="state">State:</label>
                <input type="text" id="state" value="CA">
            </div>
            <div class="form-group">
                <label for="zipCode">Zip Code:</label>
                <input type="text" id="zipCode" value="12345">
            </div>
        </div>
        <div class="controls">
            <button class="btn-primary" onclick="updatePersonJSON()">Update Person (JSON)</button>
            <button class="btn-success" onclick="updatePersonProtobuf()">Update Person (Protobuf)</button>
        </div>
    </div>

    <div class="container">
        <h2>Results Log</h2>
        <div class="results" id="results"></div>
    </div>

    <div class="container">
        <h2>Persons List</h2>
        <div class="persons-list" id="personsList"></div>
    </div>

    <script src="protobuf-demo.js"></script>
    <script src="app.js"></script>
</body>
</html>
