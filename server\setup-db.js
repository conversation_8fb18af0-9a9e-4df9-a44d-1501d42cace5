const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = path.join(__dirname, 'database.sqlite');
const db = new sqlite3.Database(dbPath);

// Create the persons table
const createTableSQL = `
  CREATE TABLE IF NOT EXISTS persons (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    email TEXT UNIQUE NOT NULL,
    phone TEXT,
    address TEXT,
    city TEXT,
    state TEXT,
    zip_code TEXT,
    country TEXT,
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL
  )
`;

// Sample data for testing
const samplePersons = [
  {
    first_name: '<PERSON>',
    last_name: '<PERSON><PERSON>',
    email: '<EMAIL>',
    phone: '******-0123',
    address: '123 Main St',
    city: 'Anytown',
    state: 'CA',
    zip_code: '12345',
    country: 'USA'
  },
  {
    first_name: '<PERSON>',
    last_name: '<PERSON>',
    email: '<EMAIL>',
    phone: '******-0456',
    address: '456 Oak Ave',
    city: 'Springfield',
    state: 'IL',
    zip_code: '62701',
    country: 'USA'
  },
  {
    first_name: 'Bob',
    last_name: 'Johnson',
    email: '<EMAIL>',
    phone: '******-0789',
    address: '789 Pine Rd',
    city: 'Madison',
    state: 'WI',
    zip_code: '53703',
    country: 'USA'
  },
  {
    first_name: 'Alice',
    last_name: 'Williams',
    email: '<EMAIL>',
    phone: '******-0321',
    address: '321 Elm St',
    city: 'Portland',
    state: 'OR',
    zip_code: '97201',
    country: 'USA'
  },
  {
    first_name: 'Charlie',
    last_name: 'Brown',
    email: '<EMAIL>',
    phone: '******-0654',
    address: '654 Maple Dr',
    city: 'Austin',
    state: 'TX',
    zip_code: '73301',
    country: 'USA'
  }
];

db.serialize(() => {
  // Create table
  db.run(createTableSQL, (err) => {
    if (err) {
      console.error('Error creating table:', err);
      return;
    }
    console.log('Persons table created successfully');
  });

  // Clear existing data
  db.run('DELETE FROM persons', (err) => {
    if (err) {
      console.error('Error clearing table:', err);
      return;
    }
    console.log('Existing data cleared');
  });

  // Insert sample data
  const insertSQL = `
    INSERT INTO persons (first_name, last_name, email, phone, address, city, state, zip_code, country, created_at, updated_at)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `;

  const stmt = db.prepare(insertSQL);
  const now = Date.now();

  samplePersons.forEach((person, index) => {
    stmt.run([
      person.first_name,
      person.last_name,
      person.email,
      person.phone,
      person.address,
      person.city,
      person.state,
      person.zip_code,
      person.country,
      now,
      now
    ], (err) => {
      if (err) {
        console.error(`Error inserting person ${index + 1}:`, err);
      } else {
        console.log(`Inserted person: ${person.first_name} ${person.last_name}`);
      }
    });
  });

  stmt.finalize();
});

db.close((err) => {
  if (err) {
    console.error('Error closing database:', err);
  } else {
    console.log('Database setup completed successfully');
  }
});
