// Global variables to store performance data
let jsonResponseTime = 0;
let protobufResponseTime = 0;
let personsData = [];

// Utility functions
function logMessage(message, type = 'info') {
    const resultsDiv = document.getElementById('results');
    const logEntry = document.createElement('div');
    logEntry.className = `log-entry log-${type}`;
    logEntry.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
    resultsDiv.appendChild(logEntry);
    resultsDiv.scrollTop = resultsDiv.scrollHeight;
}

function updatePerformanceStats() {
    document.getElementById('jsonTime').textContent = jsonResponseTime;
    document.getElementById('protobufTime').textContent = protobufResponseTime;
    
    if (jsonResponseTime > 0 && protobufResponseTime > 0) {
        const comparison = document.getElementById('comparison');
        const comparisonText = document.getElementById('comparisonText');
        
        if (protobufResponseTime < jsonResponseTime) {
            const improvement = ((jsonResponseTime - protobufResponseTime) / jsonResponseTime * 100).toFixed(1);
            comparisonText.textContent = `Protobuf is ${improvement}% faster than JSON`;
            comparisonText.style.color = '#28a745';
        } else if (jsonResponseTime < protobufResponseTime) {
            const slower = ((protobufResponseTime - jsonResponseTime) / jsonResponseTime * 100).toFixed(1);
            comparisonText.textContent = `JSON is ${slower}% faster than Protobuf`;
            comparisonText.style.color = '#dc3545';
        } else {
            comparisonText.textContent = 'Both APIs performed equally';
            comparisonText.style.color = '#6c757d';
        }
        
        comparison.style.display = 'block';
    }
}

function displayPersons(persons) {
    const personsList = document.getElementById('personsList');
    personsList.innerHTML = '';
    
    persons.forEach(person => {
        const personCard = document.createElement('div');
        personCard.className = 'person-card';
        personCard.innerHTML = `
            <div class="person-name">${person.first_name} ${person.last_name}</div>
            <div class="person-details">
                <div><strong>ID:</strong> ${person.id}</div>
                <div><strong>Email:</strong> ${person.email}</div>
                <div><strong>Phone:</strong> ${person.phone}</div>
                <div><strong>Address:</strong> ${person.address}, ${person.city}, ${person.state} ${person.zip_code}</div>
                <div><strong>Country:</strong> ${person.country}</div>
            </div>
        `;
        personsList.appendChild(personCard);
    });
}

function getPersonFormData() {
    return {
        first_name: document.getElementById('firstName').value,
        last_name: document.getElementById('lastName').value,
        email: document.getElementById('email').value,
        phone: document.getElementById('phone').value,
        address: document.getElementById('address').value,
        city: document.getElementById('city').value,
        state: document.getElementById('state').value,
        zip_code: document.getElementById('zipCode')?.value || '',
        country: 'USA'
    };
}

// API Functions
async function loadPersonsJSON() {
    try {
        logMessage('Loading persons via JSON API...', 'info');
        const startTime = performance.now();
        
        const response = await fetch('/api/json/persons');
        const data = await response.json();
        
        const endTime = performance.now();
        jsonResponseTime = Math.round(endTime - startTime);
        
        if (data.success) {
            personsData = data.persons;
            displayPersons(data.persons);
            logMessage(`JSON API: Loaded ${data.persons.length} persons in ${jsonResponseTime}ms`, 'success');
            updatePerformanceStats();
        } else {
            logMessage(`JSON API Error: ${data.message}`, 'error');
        }
    } catch (error) {
        logMessage(`JSON API Error: ${error.message}`, 'error');
    }
}

async function loadPersonsProtobuf() {
    try {
        logMessage('Loading persons via Protobuf API...', 'info');
        const startTime = performance.now();
        
        const response = await fetch('/api/protobuf/persons', {
            headers: {
                'Accept': 'application/x-protobuf'
            }
        });
        
        // For this demo, we're still receiving JSON data but with protobuf content-type
        // In a real implementation, you'd parse actual protobuf binary data
        const text = await response.text();
        const data = JSON.parse(text);
        
        const endTime = performance.now();
        protobufResponseTime = Math.round(endTime - startTime);
        
        if (data.success) {
            personsData = data.persons;
            displayPersons(data.persons);
            logMessage(`Protobuf API: Loaded ${data.persons.length} persons in ${protobufResponseTime}ms`, 'success');
            updatePerformanceStats();
        } else {
            logMessage(`Protobuf API Error: ${data.message}`, 'error');
        }
    } catch (error) {
        logMessage(`Protobuf API Error: ${error.message}`, 'error');
    }
}

async function updatePersonJSON() {
    try {
        const personId = document.getElementById('personId').value;
        const personData = getPersonFormData();
        
        logMessage(`Updating person ${personId} via JSON API...`, 'info');
        const startTime = performance.now();
        
        const response = await fetch(`/api/json/persons/${personId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(personData)
        });
        
        const data = await response.json();
        const endTime = performance.now();
        const responseTime = Math.round(endTime - startTime);
        
        if (data.success) {
            logMessage(`JSON API: Person updated successfully in ${responseTime}ms`, 'success');
            // Refresh the persons list
            await loadPersonsJSON();
        } else {
            logMessage(`JSON API Error: ${data.message}`, 'error');
        }
    } catch (error) {
        logMessage(`JSON API Error: ${error.message}`, 'error');
    }
}

async function updatePersonProtobuf() {
    try {
        const personId = document.getElementById('personId').value;
        const personData = getPersonFormData();
        
        logMessage(`Updating person ${personId} via Protobuf API...`, 'info');
        const startTime = performance.now();
        
        // For this demo, we're sending JSON data with protobuf content-type
        // In a real implementation, you'd serialize to protobuf binary format
        const response = await fetch(`/api/protobuf/persons/${personId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/x-protobuf'
            },
            body: JSON.stringify(personData)
        });
        
        const text = await response.text();
        const data = JSON.parse(text);
        const endTime = performance.now();
        const responseTime = Math.round(endTime - startTime);
        
        if (data.success) {
            logMessage(`Protobuf API: Person updated successfully in ${responseTime}ms`, 'success');
            // Refresh the persons list
            await loadPersonsProtobuf();
        } else {
            logMessage(`Protobuf API Error: ${data.message}`, 'error');
        }
    } catch (error) {
        logMessage(`Protobuf API Error: ${error.message}`, 'error');
    }
}

async function runPerformanceTest() {
    logMessage('Starting performance test...', 'info');
    
    // Reset stats
    jsonResponseTime = 0;
    protobufResponseTime = 0;
    updatePerformanceStats();
    
    // Run multiple iterations to get average performance
    const iterations = 5;
    let jsonTotalTime = 0;
    let protobufTotalTime = 0;
    
    for (let i = 0; i < iterations; i++) {
        logMessage(`Performance test iteration ${i + 1}/${iterations}`, 'info');
        
        // Test JSON API
        const jsonStart = performance.now();
        await fetch('/api/json/persons');
        const jsonEnd = performance.now();
        jsonTotalTime += (jsonEnd - jsonStart);
        
        // Small delay between requests
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // Test Protobuf API
        const protobufStart = performance.now();
        await fetch('/api/protobuf/persons', {
            headers: { 'Accept': 'application/x-protobuf' }
        });
        const protobufEnd = performance.now();
        protobufTotalTime += (protobufEnd - protobufStart);
        
        // Small delay between iterations
        await new Promise(resolve => setTimeout(resolve, 200));
    }
    
    jsonResponseTime = Math.round(jsonTotalTime / iterations);
    protobufResponseTime = Math.round(protobufTotalTime / iterations);
    
    updatePerformanceStats();
    logMessage(`Performance test completed. Average times: JSON=${jsonResponseTime}ms, Protobuf=${protobufResponseTime}ms`, 'success');
}

function clearResults() {
    document.getElementById('results').innerHTML = '';
    jsonResponseTime = 0;
    protobufResponseTime = 0;
    updatePerformanceStats();
    document.getElementById('comparison').style.display = 'none';
    logMessage('Results cleared', 'info');
}

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    logMessage('Application initialized. Ready to test API performance!', 'success');
    
    // Load initial data
    loadPersonsJSON();
});
