syntax = "proto3";

package person;

// Person message definition
message Person {
  int32 id = 1;
  string first_name = 2;
  string last_name = 3;
  string email = 4;
  string phone = 5;
  string address = 6;
  string city = 7;
  string state = 8;
  string zip_code = 9;
  string country = 10;
  int64 created_at = 11;
  int64 updated_at = 12;
}

// Request message for updating a person
message UpdatePersonRequest {
  Person person = 1;
}

// Response message for update operations
message UpdatePersonResponse {
  bool success = 1;
  string message = 2;
  Person person = 3;
}

// Request message for getting a person by ID
message GetPersonRequest {
  int32 id = 1;
}

// Response message for get operations
message GetPersonResponse {
  bool success = 1;
  string message = 2;
  Person person = 3;
}

// Request message for getting all persons
message GetAllPersonsRequest {
  int32 limit = 1;
  int32 offset = 2;
}

// Response message for get all operations
message GetAllPersonsResponse {
  bool success = 1;
  string message = 2;
  repeated Person persons = 3;
  int32 total_count = 4;
}

// Request message for creating a person
message CreatePersonRequest {
  Person person = 1;
}

// Response message for create operations
message CreatePersonResponse {
  bool success = 1;
  string message = 2;
  Person person = 3;
}

// Request message for deleting a person
message DeletePersonRequest {
  int32 id = 1;
}

// Response message for delete operations
message DeletePersonResponse {
  bool success = 1;
  string message = 2;
}
