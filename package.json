{"name": "protobuf-api-demo", "version": "1.0.0", "description": "API demonstration comparing Protocol Buffers vs JSON performance", "main": "server/index.js", "scripts": {"start": "node server/index.js", "dev": "nodemon server/index.js", "demo": "node start-demo.js", "generate-proto": "protoc --js_out=import_style=commonjs,binary:./generated --proto_path=./proto ./proto/*.proto", "setup-db": "node server/setup-db.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["protobuf", "api", "performance", "demo"], "author": "", "license": "MIT", "dependencies": {"express": "^4.18.2", "sqlite3": "^5.1.6", "google-protobuf": "^3.21.2", "cors": "^2.8.5", "body-parser": "^1.20.2"}, "devDependencies": {"nodemon": "^3.0.1"}}